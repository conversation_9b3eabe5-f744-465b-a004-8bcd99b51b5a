import { createRouter, createWebHistory, type Router, type RouteRecordRaw } from 'vue-router'
import { beforeEachGuard, afterEachGuard } from './guards' // 导入路由守卫
import authRoutes from '@/router/routes/authRoutes'
import clientRoutes from '@/router/routes/clientRoutes'
import userRoutes from './routes/userRoutes'
import { flatten } from '@/utils/flatten'
import store from '@/store'
import type { IMenuResponse } from '@/types/menu'
import { setPageTitle } from '@/utils/system'

// 合并所有路由配置
const routes: Array<RouteRecordRaw> = [...authRoutes, ...userRoutes]

const router: Router = createRouter({
    history: createWebHistory(),
    routes,
})

router.
window.isDynamicRoutesAdded = false

router.beforeEach(async (to, _, next) => {
    console.log('store',store)

    const token = localStorage.getItem('access_token')
    const { name, meta } = to
    const oemConfigFromGetter = store.getters['auth/oemConfig']
    console.log('oemConfig from getter:', oemConfigFromGetter)
    if(oemConfigFromGetter && oemConfigFromGetter.modules.length > 0 && oemConfigFromGetter.modules[0].config.webPageTabTitle){
        setPageTitle(meta?.title as string,oemConfigFromGetter.modules[0].config.webPageTabTitle)
    }

    if (token) {
        const targetIndex = authRoutes.findIndex((e) => e.name === name)
        if (targetIndex !== -1) {
            return next()
        }
    }

    if (token) {
        if (!window.isDynamicRoutesAdded) {
            const menuTree: IMenuResponse[] = await store.dispatch('menu/loadMenuTreeIfNeeded')
            const flattenMenu = flatten(menuTree)
            const routesToAdd: RouteRecordRaw[] = []

            for (const route of clientRoutes) {
                const { name: routeName = {}, meta: routeMeta = {}, children: routeChildren = [] } = route
                const hasMatchingMenu = flattenMenu.some((e) => e.menuId === routeName)
                // 决定是否添加主路由
                if (!hasMatchingMenu && !routeMeta?.rely) continue

                const newRoute: RouteRecordRaw = {
                    ...route,
                    children: [],
                }

                // 过滤并添加子路由
                newRoute.children = routeChildren.filter((child) => {
                    const { name: childName = {}, meta: childMeta = {} } = child
                    return childMeta.rely || flattenMenu.some((e) => e.menuId === childName)
                })

                routesToAdd.push(newRoute)
                router.addRoute(newRoute)
            }

            // 最后添加 404
            router.addRoute({
                path: '/:pathMatch(.*)*',
                name: '404',
                component: () => import('@/views/404/NotFoundPage.vue'),
            })

            window.isDynamicRoutesAdded = true

            next({ ...to, replace: true })
        } else {
            next()
        }
    } else {
        const { name } = to
        const targetIndex = authRoutes.findIndex((e) => e.name === name)

        if (targetIndex !== -1) {
            next()
        } else {
            next('/login')
        }
    }
})

// 使用路由守卫
router.beforeEach(beforeEachGuard)
router.afterEach(afterEachGuard)

export default router
